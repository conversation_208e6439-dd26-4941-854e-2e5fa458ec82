{"name": "browser-use-mcp-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.6.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.0.9", "cors": "^2.8.5", "express": "^4.21.2", "http-proxy-middleware": "^3.0.3", "lucide-react": "^0.477.0", "react": "^19.0.0", "react-dom": "^19.0.0", "shell-quote": "^1.8.2", "spawn-rx": "^5.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.13.8", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.9", "tailwindcss-animate": "^1.0.7", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}